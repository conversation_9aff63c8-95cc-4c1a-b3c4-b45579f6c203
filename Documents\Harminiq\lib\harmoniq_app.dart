import 'dart:math';
import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import 'package:just_audio_background/just_audio_background.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:harminiq/services/session_timer.dart';
import 'package:harminiq/services/settings.dart';
import 'package:harminiq/screens/settings_screen.dart';


// Entrypoint for the Harmoniq app
Future<void> runHarmoniqApp() async {
  WidgetsFlutterBinding.ensureInitialized();
  await JustAudioBackground.init(
    androidNotificationChannelId: 'harmoniq_channel',
    androidNotificationChannelName: 'Harmoniq Playback',
    androidNotificationOngoing: true,
  );
  runApp(const HarmoniqApp());
}

class FreqTrack {
  final String label;
  final int hz;
  final String description;
  final String assetPath;
  const FreqTrack(this.label, this.hz, this.description, this.assetPath);
}

const List<FreqTrack> kTracks = [
  FreqTrack('111 Hz', 111, 'Deep meditation', 'assets/audio/111hz.mp3'),
  FreqTrack('174 Hz', 174, 'Pain relief, safety', 'assets/audio/174hz.mp3'),
  FreqTrack('256 Hz', 256, 'Calm focus', 'assets/audio/256hz.mp3'),
  FreqTrack('285 Hz', 285, 'Tissue healing', 'assets/audio/285hz.mp3'),
  FreqTrack('396 Hz', 396, 'Release fear/guilt', 'assets/audio/396hz.mp3'),
  FreqTrack('417 Hz', 417, 'Change & cleanse', 'assets/audio/417hz.mp3'),
  FreqTrack('432 Hz', 432, 'Natural harmony', 'assets/audio/432hz.mp3'),
  FreqTrack('528 Hz', 528, 'Love & repair', 'assets/audio/528hz.mp3'),
  FreqTrack('639 Hz', 639, 'Relationships', 'assets/audio/639hz.mp3'),
  FreqTrack('741 Hz', 741, 'Detox & clarity', 'assets/audio/741hz.mp3'),
  FreqTrack('852 Hz', 852, 'Spiritual order', 'assets/audio/852hz.mp3'),
  FreqTrack('963 Hz', 963, 'Crown/pineal', 'assets/audio/963hz.mp3'),
  FreqTrack('1024 Hz', 1024, 'Mental clarity', 'assets/audio/1024hz.mp3'),
];

class HarmoniqApp extends StatelessWidget {
  const HarmoniqApp({super.key});
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Harmoniq',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        brightness: Brightness.dark,
        scaffoldBackgroundColor: Colors.black,
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple, brightness: Brightness.dark),
      ),
      home: const HomeScreen(),
    );
  }
}

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});
  @override
  Widget build(BuildContext context) {
    final cards = [
      _HomeCard(
        title: 'Quick Relief',
        subtitle: 'Instant calm + breathing',
        icon: Icons.bubble_chart,
        onTap: () => Navigator.push(context, MaterialPageRoute(builder: (_) => const QuickReliefScreen())),
      ),
      _HomeCard(
        title: 'Visual Mode',
        subtitle: 'Satisfying colorful visuals',
        icon: Icons.auto_awesome,
        onTap: () => Navigator.push(context, MaterialPageRoute(builder: (_) => const VisualModeScreen())),
      ),
      _HomeCard(
        title: 'Background Mode',
        subtitle: 'Plays with phone locked',
        icon: Icons.music_note,
        onTap: () => Navigator.push(context, MaterialPageRoute(builder: (_) => const BackgroundModeScreen())),
      ),
    ];
    return Scaffold(
      appBar: AppBar(
        title: const Text('Harmoniq'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (_) => const SettingsScreen()),
            ),
          ),
        ],
      ),
      body: ListView.separated(
        padding: const EdgeInsets.all(16),
        itemCount: cards.length,
        separatorBuilder: (_, __) => const SizedBox(height: 12),
        itemBuilder: (_, i) => cards[i],
      ),
    );
  }
}

class _HomeCard extends StatelessWidget {
  final String title, subtitle;
  final IconData icon;
  final VoidCallback onTap;
  const _HomeCard({required this.title, required this.subtitle, required this.icon, required this.onTap});
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(18),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: const LinearGradient(colors: [Color(0xFF191919), Color(0xFF2A1B3D)]),
          border: Border.all(color: Colors.white12),
        ),
        child: Row(
          children: [
            Icon(icon, size: 32, color: Colors.cyanAccent),
            const SizedBox(width: 14),
            Expanded(child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Text(title, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 4),
              Text(subtitle, style: const TextStyle(color: Colors.white70)),
            ])),
            const Icon(Icons.chevron_right, color: Colors.white54),
          ],
        ),
      ),
    );
  }
}

// Shared frequency picker
Future<FreqTrack?> showFrequencyPicker(BuildContext context, {FreqTrack? current}) {
  return showModalBottomSheet<FreqTrack>(
    context: context,
    backgroundColor: const Color(0xFF111111),
    showDragHandle: true,
    builder: (_) => ListView.separated(
      itemCount: kTracks.length,
      separatorBuilder: (_, __) => const Divider(height: 1, color: Colors.white12),
      itemBuilder: (ctx, i) {
        final t = kTracks[i];
        final selected = current?.assetPath == t.assetPath;
        return ListTile(
          title: Text('${t.label}  •  ${t.description}'),
          trailing: selected ? const Icon(Icons.check, color: Colors.cyanAccent) : null,
          onTap: () => Navigator.pop(ctx, t),
        );
      },
    ),
  );
}

// Quick Relief
class QuickReliefScreen extends StatefulWidget {
  const QuickReliefScreen({super.key});
  @override
  State<QuickReliefScreen> createState() => _QuickReliefScreenState();
}
class _QuickReliefScreenState extends State<QuickReliefScreen> with SingleTickerProviderStateMixin {
  late final AudioPlayer _player;
  late final AnimationController _breath;
  FreqTrack _current = kTracks.firstWhere((t) => t.hz == 432);
  bool _playing = false;

  @override
  void initState() {
    super.initState();
    _player = AudioPlayer();
    _breath = AnimationController(vsync: this, duration: const Duration(seconds: 6))..repeat(reverse: true);
    _loadAndPlay(_current);
  }

  Future<void> _loadAndPlay(FreqTrack t) async {
    await _player.setAudioSource(AudioSource.asset(
      t.assetPath,
      tag: MediaItem(id: t.assetPath, album: 'Harmoniq', title: t.label, artist: t.description),
    ));
    await _player.setLoopMode(LoopMode.one);
    final sp = await SharedPreferences.getInstance();
    await _player.setVolume(sp.getDouble('main_vol') ?? 0.8);
    await _player.play();
    setState(() => _playing = true);
  }

  @override
  void dispose() { _breath.dispose(); _player.dispose(); super.dispose(); }
  void _toggle() { _playing ? _player.pause() : _player.play(); setState(() => _playing = !_playing); }

  @override
  Widget build(BuildContext context) {
    final scale = Tween<double>(begin: 0.85, end: 1.15).animate(CurvedAnimation(parent: _breath, curve: Curves.easeInOut));
    return Scaffold(
      appBar: AppBar(
        title: const Text('Quick Relief'),
        actions: [
          IconButton(icon: const Icon(Icons.queue_music), onPressed: () async {
            final chosen = await showFrequencyPicker(context, current: _current);
            if (chosen != null) { _current = chosen; _loadAndPlay(_current); }
          }),
        ],
      ),
      body: Center(
        child: GestureDetector(
          onTap: _toggle,
          child: AnimatedBuilder(
            animation: _breath,
            builder: (_, __) => Transform.scale(
              scale: scale.value,
              child: Container(
                width: 220, height: 220,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: const RadialGradient(colors: [Color(0xFF5DE0E6), Color(0xFF004AAD)]),
                  boxShadow: [BoxShadow(color: Colors.cyanAccent.withOpacity(0.45), blurRadius: 36, spreadRadius: 6)],
                ),
                child: Center(child: Text(_playing ? 'Exhale' : 'Tap to Breathe', style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold))),
              ),
            ),
          ),
        ),
      ),
      bottomNavigationBar: const Padding(
        padding: EdgeInsets.fromLTRB(16, 0, 16, 20),
        child: Text('Audio continues with screen locked.',
          textAlign: TextAlign.center, style: TextStyle(color: Colors.white70)),
      ),
    );
  }
}

// Visual Mode

// Visual displays available in Visual Mode
enum VisualDisplay { waves, candle }

class VisualModeScreen extends StatefulWidget {
  const VisualModeScreen({super.key});
  @override
  State<VisualModeScreen> createState() => _VisualModeScreenState();
}
class _VisualModeScreenState extends State<VisualModeScreen> with SingleTickerProviderStateMixin {
  late final AudioPlayer _player;
  late final AudioPlayer _ambient; // second player for ambient layer
  late final AnimationController _controller;
  FreqTrack _current = kTracks.firstWhere((t) => t.hz == 528);
  bool _playing = false;

  VisualDisplay _currentDisplay = VisualDisplay.waves;
  bool _candleLit = false;
  Duration? _timerDuration; // e.g., 10 minutes
  SessionTimer? _timer;     // fires to stop playback

  @override
  void initState() {
    super.initState();
    _player = AudioPlayer();
    _ambient = AudioPlayer();
    _controller = AnimationController(vsync: this, duration: const Duration(seconds: 16))..repeat();
    _loadAndPlay(_current);

    // Load saved default hz for Visual mode (if any)
    SettingsService.getDefaultHz('visual').then((hz) async {
      if (hz != null) {
        FreqTrack? found;
        for (final t in kTracks) {
          if (t.hz == hz) { found = t; break; }
        }
        if (found != null) {
          _current = found;
          await _loadAndPlay(_current);
        }
      }
    });
  }

  Future<void> _loadAndPlay(FreqTrack t) async {
    await _player.setAudioSource(
      AudioSource.asset(
        t.assetPath,
        tag: MediaItem(id: t.assetPath, album: 'Harmoniq', title: t.label, artist: t.description),
      ),
    );
    await _player.setLoopMode(LoopMode.one);
    final sp = await SharedPreferences.getInstance();
    await _player.setVolume(sp.getDouble('main_vol') ?? 0.8);
    await _player.play();
    setState(() => _playing = true);
  }

  void _toggle() {
    if (_playing) {
      _player.pause();
    } else {
      _player.play();
    }
    setState(() => _playing = !_playing);
  }

  @override
  void dispose() {
    _controller.dispose();
    _player.dispose();
    _ambient.dispose();
    _timer?.cancel();
    super.dispose();
  }

  Future<void> _pickFreq() async {
    final chosen = await showFrequencyPicker(context, current: _current);
    if (chosen != null) { _current = chosen; _loadAndPlay(_current); }
  }

  Widget _buildControls(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: const BoxDecoration(
        color: Color(0xFF0F0F0F),
        border: Border(top: BorderSide(color: Colors.white12)),
      ),
      child: Row(
        children: [
          PopupMenuButton<Duration>(
            onSelected: (d) {
              if (d == Duration.zero) {
                _timer?.cancel();
                setState(() => _timerDuration = null);
                return;
              }
              _timerDuration = d;
              _timer?.cancel();
              _timer = SessionTimer(
                duration: d,
                onFinished: () { _player.stop(); _ambient.stop(); setState(() => _playing = false); },
              );
              _timer!.start();
            },
            itemBuilder: (_) => const [
              PopupMenuItem(value: Duration(minutes: 5), child: Text('Timer: 5m')),
              PopupMenuItem(value: Duration(minutes: 10), child: Text('Timer: 10m')),
              PopupMenuItem(value: Duration(minutes: 15), child: Text('Timer: 15m')),
              PopupMenuItem(value: Duration(minutes: 30), child: Text('Timer: 30m')),
              PopupMenuItem(value: Duration.zero, child: Text('Timer: Off')),
            ],
            child: const Icon(Icons.timer),
          ),
          const SizedBox(width: 16),
          PopupMenuButton<String>(
            onSelected: (name) async {
              await _ambient.stop();
              if (name == 'off') return;
              await _ambient.setAsset('assets/audio/ambient/$name.mp3');
              await _ambient.setLoopMode(LoopMode.one);
              final sp = await SharedPreferences.getInstance();
              await _ambient.setVolume(sp.getDouble('amb_vol') ?? 0.4);
              await _ambient.play();
            },
            itemBuilder: (_) => const [
              PopupMenuItem(value: 'off', child: Text('Ambient: Off')),
              PopupMenuItem(value: 'rain', child: Text('Ambient: Rain')),
              PopupMenuItem(value: 'ocean', child: Text('Ambient: Ocean')),
              PopupMenuItem(value: 'white_noise', child: Text('Ambient: White Noise')),
            ],
            child: const Icon(Icons.surround_sound),
          ),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.volume_down),
            onPressed: () async {
              final v = (_player.volume - 0.1).clamp(0.0, 1.0);
              await _player.setVolume(v);
            },
          ),
          IconButton(
            icon: const Icon(Icons.volume_up),
            onPressed: () async {
              final v = (_player.volume + 0.1).clamp(0.0, 1.0);
              await _player.setVolume(v);
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Visual Mode'),
        actions: [
          PopupMenuButton<VisualDisplay>(
            onSelected: (display) => setState(() => _currentDisplay = display),
            itemBuilder: (_) => const [
              PopupMenuItem(value: VisualDisplay.waves, child: Text('Waves')),
              PopupMenuItem(value: VisualDisplay.candle, child: Text('Candle')),
            ],
            child: const Icon(Icons.palette),
          ),
          IconButton(icon: const Icon(Icons.queue_music), onPressed: _pickFreq),
          IconButton(icon: Icon(_playing ? Icons.pause : Icons.play_arrow), onPressed: _toggle),
          IconButton(
            icon: const Icon(Icons.settings_backup_restore),
            tooltip: 'Set default',
            onPressed: () async { await SettingsService.saveDefaultHz('visual', _current.hz); },
          ),
        ],
      ),
      body: _buildVisualDisplay(),
      bottomNavigationBar: _buildControls(context),
    );
  }

  Widget _buildVisualDisplay() {
    switch (_currentDisplay) {
      case VisualDisplay.waves:
        return AnimatedBuilder(
          animation: _controller,
          builder: (_, __) => CustomPaint(
            painter: _WavesAndParticlesPainter(progress: _controller.value),
            size: Size.infinite,
          ),
        );
      case VisualDisplay.candle:
        return _buildCandleDisplay();
    }
  }

  Widget _buildCandleDisplay() {
    return Container(
      color: Colors.black,
      child: Center(
        child: GestureDetector(
          onTap: () => setState(() => _candleLit = !_candleLit),
          child: AnimatedBuilder(
            animation: _controller,
            builder: (_, __) => _CandleWidget(
              isLit: _candleLit,
              animationProgress: _controller.value,
            ),
          ),
        ),
      ),
    );
  }
}
class _WavesAndParticlesPainter extends CustomPainter {
  final double progress;
  _WavesAndParticlesPainter({required this.progress});
  @override
  void paint(Canvas canvas, Size size) {
    final bg = Paint()
      ..shader = LinearGradient(
        colors: [
          HSVColor.fromAHSV(1, (progress * 360) % 360, 0.6, 0.08).toColor(),
          HSVColor.fromAHSV(1, (progress * 360 + 120) % 360, 0.6, 0.10).toColor(),
        ],
        begin: Alignment.topLeft, end: Alignment.bottomRight,
      ).createShader(Offset.zero & size);
    canvas.drawRect(Offset.zero & size, bg);

    final centerY = size.height * 0.5;
    final stroke = Paint()..style = PaintingStyle.stroke..strokeWidth = 2.4..strokeCap = StrokeCap.round;
    for (int i = 0; i < 3; i++) {
      final path = Path();
      final hue = (progress * 360 + i * 120) % 360;
      stroke.color = HSVColor.fromAHSV(1, hue, 0.9, 1.0).toColor().withValues(alpha: 0.7);
      final amp = 28.0 - i * 6.0;
      final k = 1 / (38.0 + i * 14.0);
      final speed = 2 * pi * (0.18 + i * 0.03);
      for (double x = 0; x <= size.width; x++) {
        final y = centerY + sin(x * k + progress * speed + i) * amp + sin(x * (k * 0.5) + progress * speed * 0.5 + i) * (amp * 0.35);
        if (x == 0) { path.moveTo(x, y); } else { path.lineTo(x, y); }
      }
      canvas.drawPath(path, stroke);
    }

    final rnd = Random(12345);
    final p = Paint()..style = PaintingStyle.fill;
    for (int i = 0; i < 160; i++) {
      final px = rnd.nextDouble() * size.width;
      final py = rnd.nextDouble() * size.height;
      final base = 0.8 + 0.2 * sin((px + py + progress * 1000) * 0.002);
      p.color = Colors.white.withValues(alpha: (0.025 + 0.08 * base));
      canvas.drawCircle(Offset(px, py), 0.8 + 2.0 * base, p);
    }

    final glowRadius = 120.0;
    final glow = RadialGradient(colors: [Colors.white.withValues(alpha: 0.18), Colors.transparent]);
    final glowPaint = Paint()..shader = glow.createShader(Rect.fromCircle(center: Offset(size.width/2, centerY), radius: glowRadius));
    canvas.drawCircle(Offset(size.width/2, centerY), glowRadius, glowPaint);
  }
  @override
  bool shouldRepaint(covariant _WavesAndParticlesPainter old) => old.progress != progress;
}

// Candle Widget
class _CandleWidget extends StatelessWidget {
  final bool isLit;
  final double animationProgress;

  const _CandleWidget({
    required this.isLit,
    required this.animationProgress,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _CandlePainter(
        isLit: isLit,
        animationProgress: animationProgress,
      ),
      size: const Size(200, 300),
    );
  }
}

class _CandlePainter extends CustomPainter {
  final bool isLit;
  final double animationProgress;

  _CandlePainter({
    required this.isLit,
    required this.animationProgress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final centerX = size.width / 2;
    final candleWidth = size.width * 0.3;
    final candleHeight = size.height * 0.6;
    final candleTop = size.height * 0.35;

    // Draw candle body
    final candlePaint = Paint()
      ..color = const Color(0xFF8B0000) // Dark red
      ..style = PaintingStyle.fill;

    final candleRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(
        centerX - candleWidth / 2,
        candleTop,
        candleWidth,
        candleHeight,
      ),
      const Radius.circular(8),
    );
    canvas.drawRRect(candleRect, candlePaint);

    // Draw wick
    final wickPaint = Paint()
      ..color = isLit ? const Color(0xFF2C1810) : const Color(0xFF1A1A1A)
      ..style = PaintingStyle.fill;

    final wickRect = Rect.fromLTWH(
      centerX - 2,
      candleTop - 15,
      4,
      15,
    );
    canvas.drawRect(wickRect, wickPaint);

    // Draw flame if lit
    if (isLit) {
      _drawFlame(canvas, centerX, candleTop - 15, size);
    }
  }

  void _drawFlame(Canvas canvas, double centerX, double flameBase, Size size) {
    // Flame flicker animation
    final flicker = sin(animationProgress * 2 * pi * 3) * 0.1 + 1.0;
    final flameHeight = 40.0 * flicker;
    final flameWidth = 20.0 * flicker;

    // Flame gradient
    final flameGradient = RadialGradient(
      colors: [
        const Color(0xFFFFFF99), // Light yellow center
        const Color(0xFFFF6600), // Orange
        const Color(0xFFFF0000), // Red edges
      ],
      stops: const [0.0, 0.6, 1.0],
    );

    final flamePaint = Paint()
      ..shader = flameGradient.createShader(
        Rect.fromCenter(
          center: Offset(centerX, flameBase - flameHeight / 2),
          width: flameWidth,
          height: flameHeight,
        ),
      );

    // Draw flame shape
    final flamePath = Path();
    flamePath.moveTo(centerX, flameBase);
    flamePath.quadraticBezierTo(
      centerX - flameWidth / 2,
      flameBase - flameHeight * 0.3,
      centerX - flameWidth * 0.3,
      flameBase - flameHeight * 0.7,
    );
    flamePath.quadraticBezierTo(
      centerX,
      flameBase - flameHeight,
      centerX + flameWidth * 0.3,
      flameBase - flameHeight * 0.7,
    );
    flamePath.quadraticBezierTo(
      centerX + flameWidth / 2,
      flameBase - flameHeight * 0.3,
      centerX,
      flameBase,
    );

    canvas.drawPath(flamePath, flamePaint);

    // Add glow effect
    final glowPaint = Paint()
      ..color = const Color(0xFFFF6600).withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 20);

    canvas.drawPath(flamePath, glowPaint);
  }

  @override
  bool shouldRepaint(covariant _CandlePainter oldDelegate) {
    return oldDelegate.isLit != isLit ||
           oldDelegate.animationProgress != animationProgress;
  }
}

// Background Mode
class BackgroundModeScreen extends StatefulWidget {
  const BackgroundModeScreen({super.key});
  @override
  State<BackgroundModeScreen> createState() => _BackgroundModeScreenState();
}
class _BackgroundModeScreenState extends State<BackgroundModeScreen> {
  late final AudioPlayer _player;
  FreqTrack _current = kTracks.firstWhere((t) => t.hz == 639);
  bool _playing = false;

  @override
  void initState() { super.initState(); _player = AudioPlayer(); _loadAndPlay(_current); }
  Future<void> _loadAndPlay(FreqTrack t) async {
    await _player.setAudioSource(AudioSource.asset(t.assetPath, tag: MediaItem(id: t.assetPath, album: 'Harmoniq', title: t.label, artist: t.description)));
    await _player.setLoopMode(LoopMode.one);
    final sp = await SharedPreferences.getInstance();
    await _player.setVolume(sp.getDouble('main_vol') ?? 0.8);
    await _player.play();
    setState(() => _playing = true);
  }
  @override
  void dispose() { _player.dispose(); super.dispose(); }
  void _toggle(){ _playing ? _player.pause() : _player.play(); setState(() => _playing = !_playing); }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Background Mode'), actions: [
        IconButton(icon: const Icon(Icons.queue_music), onPressed: () async {
          final chosen = await showFrequencyPicker(context, current: _current);
          if (chosen != null) { _current = chosen; _loadAndPlay(_current); }
        }),
        IconButton(icon: Icon(_playing ? Icons.pause : Icons.play_arrow), onPressed: _toggle),
      ]),
      body: const Center(child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Text('Audio continues with the screen locked.\nUse device media controls to pause/play.',
          textAlign: TextAlign.center, style: TextStyle(color: Colors.white70)),
      )),
    );
  }
}

